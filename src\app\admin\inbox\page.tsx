'use client'

import { useState, useEffect } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { ProtectedRoute } from '@/components/admin/ProtectedRoute'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useToast } from '@/contexts/ToastContext'
import { ErrorModal } from '@/components/ui/error-modal'
import { useUser } from '@/contexts/UserContext'
import { useMessages } from '@/contexts/MessageContext'
import {
  Mail,
  Search,
  Trash2,
  Reply,
  MailOpen,
  Clock,
  User,
  Shield,
  Eye,
  Settings,
  Crown,
  X,
  Send,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

interface Message {
  id: string
  subject: string
  content: string
  senderEmail: string
  senderName: string
  recipientEmail: string
  recipientName: string
  senderType: 'admin' | 'user'
  recipientType: 'admin' | 'user'
  status: string
  sentAt: string
  readAt: string | null
  createdAt: string
}

export default function InboxPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null)
  const [showReplyModal, setShowReplyModal] = useState(false)
  const [replyContent, setReplyContent] = useState('')
  const [replySubject, setReplySubject] = useState('')
  const [sending, setSending] = useState(false)
  const [errorModal, setErrorModal] = useState<{
    isOpen: boolean
    type: 'error' | 'warning' | 'info' | 'success'
    title: string
    description: string
    details?: string
    errorCode?: string
  }>({
    isOpen: false,
    type: 'error',
    title: '',
    description: ''
  })

  const { success, error } = useToast()
  const { currentUser } = useUser()
  const { refreshStats, markAsRead: markMessageAsRead, deleteMessage: deleteMessageFromStats } = useMessages()

  useEffect(() => {
    fetchMessages()
  }, [])

  const fetchMessages = async () => {
    try {
      const response = await fetch('/api/admin/messages/inbox')
      if (response.ok) {
        const data = await response.json()
        setMessages(data.messages || [])
        // Refresh message stats after fetching messages
        refreshStats()
      } else {
        throw new Error('Failed to fetch messages')
      }
    } catch (error) {
      setErrorModal({
        isOpen: true,
        type: 'error',
        title: 'Failed to Load Messages',
        description: 'Unable to load your inbox messages. Please refresh the page or contact support.',
        details: `Error: ${error instanceof Error ? error.message : 'Unknown error'}\nTime: ${new Date().toISOString()}`,
        errorCode: 'INBOX_LOAD_ERROR'
      })
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (messageId: string) => {
    try {
      const response = await fetch(`/api/admin/messages/${messageId}/read`, {
        method: 'PUT'
      })

      if (response.ok) {
        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? { ...msg, readAt: new Date().toISOString() }
            : msg
        ))
        // Update message stats
        markMessageAsRead(messageId)
      }
    } catch (error) {
      console.error('Failed to mark message as read:', error)
    }
  }

  const deleteMessage = async (messageId: string) => {
    try {
      const response = await fetch(`/api/admin/messages/${messageId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setMessages(prev => prev.filter(msg => msg.id !== messageId))
        setSelectedMessage(null)
        // Update message stats
        deleteMessageFromStats(messageId)
        success('Message Deleted', 'The message has been deleted successfully.')
      } else {
        throw new Error('Failed to delete message')
      }
    } catch (error) {
      error('Delete Failed', 'Unable to delete the message. Please try again.')
    }
  }

  const handleReply = async () => {
    if (!selectedMessage || !replyContent.trim() || !replySubject.trim()) {
      error('Missing Information', 'Please provide both subject and message for the reply.')
      return
    }

    setSending(true)

    try {
      const response = await fetch('/api/admin/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          recipientId: selectedMessage.senderEmail, // Use email as identifier
          recipientType: selectedMessage.senderType,
          subject: replySubject,
          message: replyContent
        })
      })

      if (response.ok) {
        success('Reply Sent', 'Your reply has been sent successfully.')
        setShowReplyModal(false)
        setReplyContent('')
        setReplySubject('')
        // Refresh message stats as the recipient will have a new unread message
        refreshStats()
      } else {
        throw new Error('Failed to send reply')
      }
    } catch (error) {
      error('Reply Failed', 'Unable to send your reply. Please try again.')
    } finally {
      setSending(false)
    }
  }

  const getRoleIcon = (senderType: string, senderName: string) => {
    // This is a simplified role detection - in a real app you'd have role info
    if (senderName.includes('Super Admin') || senderName.includes('Admin')) {
      return <Crown className="h-4 w-4 text-purple-600" />
    }
    if (senderName.includes('Manager')) {
      return <Settings className="h-4 w-4 text-green-600" />
    }
    if (senderName.includes('Staff')) {
      return <User className="h-4 w-4 text-orange-600" />
    }
    return <Eye className="h-4 w-4 text-gray-600" />
  }

  const filteredMessages = messages.filter(message =>
    message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.senderName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.content.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const unreadCount = messages.filter(msg => !msg.readAt).length

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString('en-US', { weekday: 'short' })
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      })
    }
  }

  if (loading) {
    return (
      <AdminLayoutNew title="Inbox" description="View and manage your messages">
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </AdminLayoutNew>
    )
  }

  return (
    <ProtectedRoute requiredRoles={['Super Admin', 'Admin', 'Manager', 'Staff', 'Viewer']}>
      <AdminLayoutNew title="Inbox" description="View and manage your messages">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Total Messages</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{messages.length}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                <Mail className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Unread Messages</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{unreadCount}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                <MailOpen className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">Read Messages</p>
                <p className="font-apercu-bold text-2xl text-gray-900">{messages.length - unreadCount}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-apercu-medium text-sm text-gray-600 mb-1">This Week</p>
                <p className="font-apercu-bold text-2xl text-gray-900">
                  {messages.filter(msg => {
                    const msgDate = new Date(msg.sentAt)
                    const weekAgo = new Date()
                    weekAgo.setDate(weekAgo.getDate() - 7)
                    return msgDate > weekAgo
                  }).length}
                </p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <Clock className="h-5 w-5 text-white" />
              </div>
            </div>
          </Card>
        </div>

        {/* Search */}
        <Card className="p-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search messages by subject, sender, or content..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 font-apercu-regular"
            />
          </div>
        </Card>

        {/* Messages Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Message List */}
          <div className="lg:col-span-1">
            <Card className="p-4">
              <h3 className="font-apercu-bold text-lg text-gray-900 mb-4">
                Messages ({filteredMessages.length})
              </h3>

              {filteredMessages.length === 0 ? (
                <div className="text-center py-8">
                  <Mail className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="font-apercu-medium text-gray-600">No messages found</p>
                  <p className="font-apercu-regular text-sm text-gray-500">
                    {searchTerm ? 'Try adjusting your search criteria' : 'Your inbox is empty'}
                  </p>
                </div>
              ) : (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {filteredMessages.map(message => (
                    <div
                      key={message.id}
                      onClick={() => {
                        setSelectedMessage(message)
                        if (!message.readAt) {
                          markAsRead(message.id)
                        }
                      }}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedMessage?.id === message.id
                          ? 'bg-indigo-50 border border-indigo-200'
                          : message.readAt
                          ? 'bg-gray-50 hover:bg-gray-100'
                          : 'bg-blue-50 hover:bg-blue-100 border border-blue-200'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex items-center space-x-2">
                          <div className="h-8 w-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <span className="font-apercu-bold text-white text-xs">
                              {message.senderName.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-apercu-bold text-sm text-gray-900 truncate">
                              {message.senderName}
                            </p>
                            <p className="font-apercu-regular text-xs text-gray-600">
                              {formatDate(message.sentAt)}
                            </p>
                          </div>
                        </div>
                        {!message.readAt && (
                          <div className="h-2 w-2 bg-blue-600 rounded-full"></div>
                        )}
                      </div>
                      <p className="font-apercu-medium text-sm text-gray-900 truncate mb-1">
                        {message.subject}
                      </p>
                      <p className="font-apercu-regular text-xs text-gray-600 line-clamp-2">
                        {message.content}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          </div>

          {/* Message Detail */}
          <div className="lg:col-span-2">
            {selectedMessage ? (
              <Card className="p-6">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-start space-x-4">
                    <div className="h-12 w-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <span className="font-apercu-bold text-white">
                        {selectedMessage.senderName.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-apercu-bold text-lg text-gray-900">
                          {selectedMessage.senderName}
                        </h3>
                        {getRoleIcon(selectedMessage.senderType, selectedMessage.senderName)}
                      </div>
                      <p className="font-apercu-regular text-sm text-gray-600">
                        {selectedMessage.senderEmail}
                      </p>
                      <p className="font-apercu-regular text-xs text-gray-500">
                        {new Date(selectedMessage.sentAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setReplySubject(`Re: ${selectedMessage.subject}`)
                        setShowReplyModal(true)
                      }}
                      className="font-apercu-medium"
                    >
                      <Reply className="h-4 w-4 mr-1" />
                      Reply
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => deleteMessage(selectedMessage.id)}
                      className="font-apercu-medium"
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>

                <div className="border-b border-gray-200 pb-4 mb-6">
                  <h2 className="font-apercu-bold text-xl text-gray-900">
                    {selectedMessage.subject}
                  </h2>
                </div>

                <div className="prose max-w-none">
                  <div className="font-apercu-regular text-gray-900 whitespace-pre-wrap leading-relaxed">
                    {selectedMessage.content}
                  </div>
                </div>
              </Card>
            ) : (
              <Card className="p-6">
                <div className="text-center py-12">
                  <Mail className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="font-apercu-bold text-lg text-gray-900 mb-2">
                    Select a Message
                  </h3>
                  <p className="font-apercu-regular text-gray-600">
                    Choose a message from the list to view its contents
                  </p>
                </div>
              </Card>
            )}
          </div>
        </div>

        {/* Reply Modal */}
        {showReplyModal && selectedMessage && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
              <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-white/20 rounded-lg flex items-center justify-center">
                      <Reply className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-apercu-bold text-xl text-white">Reply to Message</h3>
                      <p className="font-apercu-regular text-indigo-100 text-sm">
                        Replying to {selectedMessage.senderName}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowReplyModal(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                    Subject *
                  </label>
                  <Input
                    value={replySubject}
                    onChange={(e) => setReplySubject(e.target.value)}
                    placeholder="Enter reply subject..."
                    className="font-apercu-regular"
                  />
                </div>

                <div>
                  <label className="block font-apercu-medium text-sm text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    value={replyContent}
                    onChange={(e) => setReplyContent(e.target.value)}
                    placeholder="Type your reply here..."
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-apercu-regular focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                  />
                </div>
              </div>

              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowReplyModal(false)}
                    disabled={sending}
                    className="font-apercu-medium"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleReply}
                    disabled={sending || !replyContent.trim() || !replySubject.trim()}
                    className="font-apercu-medium"
                  >
                    {sending ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Send Reply
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error Modal */}
        <ErrorModal
          isOpen={errorModal.isOpen}
          onClose={() => setErrorModal(prev => ({ ...prev, isOpen: false }))}
          type={errorModal.type}
          title={errorModal.title}
          description={errorModal.description}
          details={errorModal.details}
          errorCode={errorModal.errorCode}
        />
      </AdminLayoutNew>
    </ProtectedRoute>
  )
}
