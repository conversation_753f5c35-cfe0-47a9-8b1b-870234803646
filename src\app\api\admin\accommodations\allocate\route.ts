import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { authenticateRequest } from '@/lib/auth-helpers'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to allocate rooms
    if (!['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const data = await request.json()
    const { ageRangeYears, allocateAll = false } = data

    // Validate age range
    if (!ageRangeYears || typeof ageRangeYears !== 'number' || ageRangeYears < 1) {
      return NextResponse.json(
        { error: 'Age range in years is required and must be a positive number' },
        { status: 400 }
      )
    }

    // Get all unallocated registrations
    const unallocatedRegistrations = await prisma.registration.findMany({
      where: {
        roomAllocation: null
      },
      orderBy: [
        { gender: 'asc' },
        { dateOfBirth: 'desc' } // Older participants first
      ]
    })

    if (unallocatedRegistrations.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No unallocated registrations found',
        allocations: []
      })
    }

    // Get all active rooms
    const rooms = await prisma.room.findMany({
      where: { isActive: true },
      include: {
        allocations: true
      },
      orderBy: { name: 'asc' }
    })

    if (rooms.length === 0) {
      return NextResponse.json(
        { error: 'No active rooms available for allocation' },
        { status: 400 }
      )
    }

    // Calculate age for each registration
    const registrationsWithAge = unallocatedRegistrations.map(reg => {
      const today = new Date()
      const birthDate = new Date(reg.dateOfBirth)
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }
      return { ...reg, age }
    })

    // Group registrations by gender and age groups
    const groupedRegistrations = registrationsWithAge.reduce((groups, reg) => {
      // Calculate age group based on the entered range
      // For example, if ageRangeYears is 5 and age is 23, it should be in group 20-24
      const minAge = Math.floor(reg.age / ageRangeYears) * ageRangeYears
      const maxAge = minAge + ageRangeYears - 1
      const key = `${reg.gender}-${minAge}-${maxAge}`

      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(reg)

      return groups
    }, {} as Record<string, typeof registrationsWithAge>)

    const allocations = []
    const allocationResults = []

    // Process each group
    for (const [groupKey, groupRegistrations] of Object.entries(groupedRegistrations)) {
      const [gender, minAgeStr, maxAgeStr] = groupKey.split('-')
      const minAge = parseInt(minAgeStr)
      const maxAge = parseInt(maxAgeStr)

      // Find suitable rooms for this group
      const suitableRooms = rooms.filter(room => 
        room.gender === gender && 
        room.allocations.length < room.capacity
      ).sort((a, b) => {
        // Prioritize rooms with more available space
        const aAvailable = a.capacity - a.allocations.length
        const bAvailable = b.capacity - b.allocations.length
        return bAvailable - aAvailable
      })

      if (suitableRooms.length === 0) {
        allocationResults.push({
          group: `${gender} (${minAge}-${maxAge} years)`,
          count: groupRegistrations.length,
          status: 'failed',
          reason: `No suitable ${gender.toLowerCase()} rooms available`
        })
        continue
      }

      // Allocate registrations to rooms
      let registrationIndex = 0
      for (const room of suitableRooms) {
        const availableSpaces = room.capacity - room.allocations.length
        const toAllocate = Math.min(availableSpaces, groupRegistrations.length - registrationIndex)

        for (let i = 0; i < toAllocate; i++) {
          const registration = groupRegistrations[registrationIndex + i]
          allocations.push({
            registrationId: registration.id,
            roomId: room.id,
            allocatedBy: currentUser.email
          })
          
          // Update room allocations count for next iteration
          room.allocations.push({ id: 'temp' } as any)
        }

        registrationIndex += toAllocate

        if (registrationIndex >= groupRegistrations.length) {
          break
        }
      }

      const allocated = registrationIndex
      const remaining = groupRegistrations.length - allocated

      allocationResults.push({
        group: `${gender} (${minAge}-${maxAge} years)`,
        count: groupRegistrations.length,
        allocated,
        remaining,
        status: remaining === 0 ? 'success' : 'partial'
      })
    }

    // Create allocations in database
    if (allocations.length > 0) {
      await prisma.roomAllocation.createMany({
        data: allocations
      })
    }

    return NextResponse.json({
      success: true,
      message: `Successfully allocated ${allocations.length} registrations`,
      totalProcessed: unallocatedRegistrations.length,
      totalAllocated: allocations.length,
      allocationResults,
      ageRangeYears
    })

  } catch (error) {
    console.error('Error allocating rooms:', error)
    return NextResponse.json(
      { error: 'Failed to allocate rooms' },
      { status: 500 }
    )
  }
}
