'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

interface FetchOptions {
  enabled?: boolean
  refetchInterval?: number
  staleTime?: number
  cacheTime?: number
  retryCount?: number
  retryDelay?: number
}

interface FetchState<T> {
  data: T | null
  loading: boolean
  error: string | null
  lastFetched: number | null
}

// Simple in-memory cache
const cache = new Map<string, { data: any; timestamp: number; staleTime: number }>()

export function useOptimizedFetch<T>(
  url: string,
  options: FetchOptions = {}
) {
  const {
    enabled = true,
    refetchInterval = 0,
    staleTime = 5 * 60 * 1000, // 5 minutes default
    cacheTime = 10 * 60 * 1000, // 10 minutes default
    retryCount = 3,
    retryDelay = 1000
  } = options

  const [state, setState] = useState<FetchState<T>>({
    data: null,
    loading: false,
    error: null,
    lastFetched: null
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const fetchData = useCallback(async (retries = 0): Promise<void> => {
    if (!enabled) return

    // Check cache first
    const cached = cache.get(url)
    const now = Date.now()
    
    if (cached && (now - cached.timestamp) < cached.staleTime) {
      setState(prev => ({
        ...prev,
        data: cached.data,
        loading: false,
        error: null,
        lastFetched: cached.timestamp
      }))
      return
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const response = await fetch(url, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      const timestamp = Date.now()

      // Update cache
      cache.set(url, { data, timestamp, staleTime })

      setState({
        data,
        loading: false,
        error: null,
        lastFetched: timestamp
      })

    } catch (error: any) {
      if (error.name === 'AbortError') {
        return // Request was cancelled
      }

      console.error(`Fetch error for ${url}:`, error)

      // Retry logic
      if (retries < retryCount) {
        retryTimeoutRef.current = setTimeout(() => {
          fetchData(retries + 1)
        }, retryDelay * Math.pow(2, retries)) // Exponential backoff
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: error.message || 'Failed to fetch data'
        }))
      }
    }
  }, [url, enabled, retryCount, retryDelay, staleTime])

  const refetch = useCallback(() => {
    // Clear cache for this URL to force fresh fetch
    cache.delete(url)
    return fetchData()
  }, [fetchData, url])

  // Initial fetch
  useEffect(() => {
    if (enabled) {
      fetchData()
    }
  }, [fetchData, enabled])

  // Set up interval for refetching
  useEffect(() => {
    if (enabled && refetchInterval > 0) {
      intervalRef.current = setInterval(fetchData, refetchInterval)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [fetchData, enabled, refetchInterval])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    ...state,
    refetch,
    isStale: state.lastFetched ? (Date.now() - state.lastFetched) > staleTime : true
  }
}

// Cache cleanup utility
export function clearCache(pattern?: string) {
  if (pattern) {
    for (const key of cache.keys()) {
      if (key.includes(pattern)) {
        cache.delete(key)
      }
    }
  } else {
    cache.clear()
  }
}

// Preload data utility
export function preloadData(url: string, staleTime = 5 * 60 * 1000) {
  fetch(url)
    .then(response => response.json())
    .then(data => {
      cache.set(url, { data, timestamp: Date.now(), staleTime })
    })
    .catch(error => {
      console.warn(`Failed to preload ${url}:`, error)
    })
}
