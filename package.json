{"name": "youth", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.8.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "component-emitter": "^2.0.0", "emitter": "^0.0.5", "events": "^3.3.0", "html-pdf-node": "^1.0.8", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "15.3.2", "nodemailer": "^7.0.3", "prisma": "^6.8.2", "puppeteer": "^24.9.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "20.17.57", "@types/react": "19.1.6", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "5.8.3"}}