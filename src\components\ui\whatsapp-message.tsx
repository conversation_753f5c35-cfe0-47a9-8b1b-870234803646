'use client'

import React from 'react'
import { Check, Check<PERSON><PERSON><PERSON>, Clock, User } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface WhatsAppMessageProps {
  id: string
  content: string
  timestamp: string
  isOwn: boolean
  senderName?: string
  isRead?: boolean
  isDelivered?: boolean
  isPending?: boolean
  avatar?: string
  showSenderName?: boolean
}

export function WhatsAppMessage({
  id,
  content,
  timestamp,
  isOwn,
  senderName,
  isRead = false,
  isDelivered = false,
  isPending = false,
  avatar,
  showSenderName = true
}: WhatsAppMessageProps) {
  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const getStatusIcon = () => {
    if (isPending) {
      return <Clock className="h-3 w-3 text-gray-400" />
    }
    if (isRead) {
      return <CheckCheck className="h-3 w-3 text-blue-500" />
    }
    if (isDelivered) {
      return <CheckCheck className="h-3 w-3 text-gray-400" />
    }
    return <Check className="h-3 w-3 text-gray-400" />
  }

  return (
    <div className={cn(
      "flex w-full mb-3",
      isOwn ? "justify-end" : "justify-start"
    )}>
      <div className={cn(
        "flex max-w-[70%] gap-2",
        isOwn ? "flex-row-reverse" : "flex-row"
      )}>
        {/* Avatar */}
        {!isOwn && (
          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xs font-apercu-bold">
            {avatar ? (
              <img src={avatar} alt={senderName} className="w-8 h-8 rounded-full object-cover" />
            ) : (
              <User className="h-4 w-4" />
            )}
          </div>
        )}

        {/* Message Bubble */}
        <div className={cn(
          "relative px-3 py-2 rounded-2xl shadow-sm",
          isOwn 
            ? "bg-blue-500 text-white rounded-br-md" 
            : "bg-white border border-gray-200 text-gray-900 rounded-bl-md"
        )}>
          {/* Sender Name */}
          {!isOwn && showSenderName && senderName && (
            <div className="text-xs font-apercu-medium text-blue-600 mb-1">
              {senderName}
            </div>
          )}

          {/* Message Content */}
          <div className="font-apercu-regular text-sm leading-relaxed whitespace-pre-wrap break-words">
            {content}
          </div>

          {/* Timestamp and Status */}
          <div className={cn(
            "flex items-center justify-end gap-1 mt-1 text-xs",
            isOwn ? "text-blue-100" : "text-gray-500"
          )}>
            <span className="font-apercu-regular">
              {formatTime(timestamp)}
            </span>
            {isOwn && (
              <div className="flex-shrink-0">
                {getStatusIcon()}
              </div>
            )}
          </div>

          {/* Message Tail */}
          <div className={cn(
            "absolute top-0 w-0 h-0",
            isOwn 
              ? "right-0 border-l-[8px] border-l-blue-500 border-t-[8px] border-t-transparent" 
              : "left-0 border-r-[8px] border-r-white border-t-[8px] border-t-transparent"
          )} />
        </div>
      </div>
    </div>
  )
}

// Date Separator Component
export function DateSeparator({ date }: { date: string }) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
  }

  return (
    <div className="flex items-center justify-center my-4">
      <div className="bg-gray-100 text-gray-600 text-xs font-apercu-medium px-3 py-1 rounded-full">
        {formatDate(date)}
      </div>
    </div>
  )
}

// Typing Indicator Component
export function TypingIndicator({ senderName }: { senderName: string }) {
  return (
    <div className="flex justify-start mb-3">
      <div className="flex max-w-[70%] gap-2">
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-gray-400 to-gray-500 flex items-center justify-center text-white text-xs font-apercu-bold">
          <User className="h-4 w-4" />
        </div>
        
        <div className="relative px-3 py-2 bg-white border border-gray-200 rounded-2xl rounded-bl-md shadow-sm">
          <div className="text-xs font-apercu-medium text-gray-600 mb-1">
            {senderName}
          </div>
          <div className="flex items-center space-x-1">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
            <span className="text-xs text-gray-500 font-apercu-regular ml-2">typing...</span>
          </div>
          
          {/* Message Tail */}
          <div className="absolute left-0 top-0 w-0 h-0 border-r-[8px] border-r-white border-t-[8px] border-t-transparent" />
        </div>
      </div>
    </div>
  )
}

// Message Input Component
export function MessageInput({ 
  value, 
  onChange, 
  onSend, 
  placeholder = "Type a message...",
  disabled = false 
}: {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  placeholder?: string
  disabled?: boolean
}) {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      if (value.trim() && !disabled) {
        onSend()
      }
    }
  }

  return (
    <div className="flex items-end gap-2 p-3 bg-white border-t border-gray-200">
      <div className="flex-1 relative">
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          disabled={disabled}
          rows={1}
          className="w-full px-4 py-2 border border-gray-300 rounded-full resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-apercu-regular text-sm max-h-20 overflow-y-auto"
          style={{ minHeight: '40px' }}
        />
      </div>
      <button
        onClick={onSend}
        disabled={!value.trim() || disabled}
        className="flex-shrink-0 w-10 h-10 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-full flex items-center justify-center transition-colors"
      >
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
        </svg>
      </button>
    </div>
  )
}
