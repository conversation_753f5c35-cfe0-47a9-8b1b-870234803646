import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { authenticateRequest } from '@/lib/auth-helpers'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to allocate rooms
    if (!['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const data = await request.json()
    const { registrationId, roomId } = data

    // Validate required fields
    if (!registrationId || !roomId) {
      return NextResponse.json(
        { error: 'Registration ID and Room ID are required' },
        { status: 400 }
      )
    }

    // Check if registration exists and is not already allocated
    const registration = await prisma.registration.findUnique({
      where: { id: registrationId },
      include: { roomAllocation: true }
    })

    if (!registration) {
      return NextResponse.json(
        { error: 'Registration not found' },
        { status: 404 }
      )
    }

    if (registration.roomAllocation) {
      return NextResponse.json(
        { error: 'Registration is already allocated to a room' },
        { status: 400 }
      )
    }

    // Check if room exists and has available space
    const room = await prisma.room.findUnique({
      where: { id: roomId },
      include: { allocations: true }
    })

    if (!room) {
      return NextResponse.json(
        { error: 'Room not found' },
        { status: 404 }
      )
    }

    if (!room.isActive) {
      return NextResponse.json(
        { error: 'Room is not active' },
        { status: 400 }
      )
    }

    if (room.allocations.length >= room.capacity) {
      return NextResponse.json(
        { error: 'Room is at full capacity' },
        { status: 400 }
      )
    }

    // Check gender compatibility
    if (room.gender !== registration.gender) {
      return NextResponse.json(
        { error: `Cannot allocate ${registration.gender.toLowerCase()} registration to ${room.gender.toLowerCase()} room` },
        { status: 400 }
      )
    }

    // Create the allocation
    const allocation = await prisma.roomAllocation.create({
      data: {
        registrationId,
        roomId,
        allocatedBy: currentUser.email
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Registration allocated successfully',
      allocation
    })

  } catch (error) {
    console.error('Error in manual allocation:', error)
    return NextResponse.json(
      { error: 'Failed to allocate registration' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authenticateRequest(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status || 401 })
    }

    const currentUser = authResult.user!

    // Check if user has permission to remove allocations
    if (!['Super Admin', 'Admin', 'Manager'].includes(currentUser.role?.name || '')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const registrationId = searchParams.get('registrationId')

    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required' },
        { status: 400 }
      )
    }

    // Find and delete the allocation
    const allocation = await prisma.roomAllocation.findUnique({
      where: { registrationId }
    })

    if (!allocation) {
      return NextResponse.json(
        { error: 'Allocation not found' },
        { status: 404 }
      )
    }

    await prisma.roomAllocation.delete({
      where: { registrationId }
    })

    return NextResponse.json({
      success: true,
      message: 'Allocation removed successfully'
    })

  } catch (error) {
    console.error('Error removing allocation:', error)
    return NextResponse.json(
      { error: 'Failed to remove allocation' },
      { status: 500 }
    )
  }
}
