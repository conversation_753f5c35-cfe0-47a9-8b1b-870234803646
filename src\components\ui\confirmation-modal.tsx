'use client'

import React from 'react'
import { AlertTriangle, CheckCircle, Info, XCircle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'

export interface ConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  message: string
  type?: 'warning' | 'danger' | 'info' | 'success'
  confirmText?: string
  cancelText?: string
  confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary'
  loading?: boolean
  icon?: React.ReactNode
}

export function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  type = 'warning',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant,
  loading = false,
  icon
}: ConfirmationModalProps) {
  if (!isOpen) return null

  const getIcon = () => {
    if (icon) return icon
    
    switch (type) {
      case 'danger':
        return <XCircle className="h-8 w-8 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-8 w-8 text-amber-600" />
      case 'info':
        return <Info className="h-8 w-8 text-blue-600" />
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-600" />
    }
  }

  const getColors = () => {
    switch (type) {
      case 'danger':
        return {
          bg: 'bg-red-50',
          border: 'border-red-200',
          iconBg: 'bg-red-100',
          titleColor: 'text-red-900',
          messageColor: 'text-red-700'
        }
      case 'warning':
        return {
          bg: 'bg-amber-50',
          border: 'border-amber-200',
          iconBg: 'bg-amber-100',
          titleColor: 'text-amber-900',
          messageColor: 'text-amber-700'
        }
      case 'info':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          iconBg: 'bg-blue-100',
          titleColor: 'text-blue-900',
          messageColor: 'text-blue-700'
        }
      case 'success':
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          iconBg: 'bg-green-100',
          titleColor: 'text-green-900',
          messageColor: 'text-green-700'
        }
    }
  }

  const colors = getColors()
  const defaultConfirmVariant = type === 'danger' ? 'destructive' : 'default'

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    } else if (e.key === 'Enter' && !loading) {
      onConfirm()
    }
  }

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm animate-in fade-in duration-200"
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <Card className={cn(
        "w-full max-w-md mx-auto shadow-2xl border-2 animate-in zoom-in-95 duration-200",
        colors.bg,
        colors.border
      )}>
        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-4">
              <div className={cn(
                "flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center",
                colors.iconBg
              )}>
                {getIcon()}
              </div>
              <div className="flex-1">
                <h3 className={cn(
                  "font-apercu-bold text-lg leading-6",
                  colors.titleColor
                )}>
                  {title}
                </h3>
              </div>
            </div>
            <button
              onClick={onClose}
              className="flex-shrink-0 p-1 rounded-full hover:bg-gray-200 transition-colors"
              disabled={loading}
            >
              <X className="h-5 w-5 text-gray-400" />
            </button>
          </div>

          {/* Message */}
          <div className="mb-6 ml-16">
            <p className={cn(
              "font-apercu-regular text-sm leading-relaxed",
              colors.messageColor
            )}>
              {message}
            </p>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-3 sm:justify-end">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
              className="font-apercu-medium order-2 sm:order-1"
            >
              {cancelText}
            </Button>
            <Button
              variant={confirmVariant || defaultConfirmVariant}
              onClick={onConfirm}
              disabled={loading}
              className="font-apercu-medium order-1 sm:order-2"
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                confirmText
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}

// Hook for easier usage
export function useConfirmation() {
  const [confirmation, setConfirmation] = React.useState<{
    isOpen: boolean
    title: string
    message: string
    type?: 'warning' | 'danger' | 'info' | 'success'
    confirmText?: string
    cancelText?: string
    onConfirm?: () => void
    loading?: boolean
  }>({
    isOpen: false,
    title: '',
    message: '',
    type: 'warning'
  })

  const confirm = React.useCallback((options: {
    title: string
    message: string
    type?: 'warning' | 'danger' | 'info' | 'success'
    confirmText?: string
    cancelText?: string
    onConfirm: () => void
  }) => {
    return new Promise<boolean>((resolve) => {
      setConfirmation({
        isOpen: true,
        ...options,
        onConfirm: () => {
          options.onConfirm()
          setConfirmation(prev => ({ ...prev, isOpen: false }))
          resolve(true)
        }
      })
    })
  }, [])

  const close = React.useCallback(() => {
    setConfirmation(prev => ({ ...prev, isOpen: false }))
  }, [])

  const setLoading = React.useCallback((loading: boolean) => {
    setConfirmation(prev => ({ ...prev, loading }))
  }, [])

  const ConfirmationComponent = React.useCallback(() => (
    <ConfirmationModal
      isOpen={confirmation.isOpen}
      onClose={close}
      onConfirm={confirmation.onConfirm || (() => {})}
      title={confirmation.title}
      message={confirmation.message}
      type={confirmation.type}
      confirmText={confirmation.confirmText}
      cancelText={confirmation.cancelText}
      loading={confirmation.loading}
    />
  ), [confirmation, close])

  return {
    confirm,
    close,
    setLoading,
    ConfirmationModal: ConfirmationComponent
  }
}
