'use client'

import { useState, useEffect } from 'react'
import { AdminLayoutNew } from '@/components/admin/AdminLayoutNew'
import { ProtectedRoute } from '@/components/admin/ProtectedRoute'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/contexts/ToastContext'
import { ErrorModal } from '@/components/ui/error-modal'
import { parseApiError } from '@/lib/error-messages'
import { useUser } from '@/contexts/UserContext'
import { RoomCard } from '@/components/admin/RoomCard'
import { RoomSetupModal } from '@/components/admin/RoomSetupModal'
import { AllocationSetupModal } from '@/components/admin/AllocationSetupModal'
import { AccommodationSearchExport } from '@/components/admin/AccommodationSearchExport'
import { PersonPreviewModal } from '@/components/admin/PersonPreviewModal'
import { PaginationControls } from '@/components/admin/PaginationControls'
import { GenderTabs, GenderTabContent } from '@/components/ui/gender-tabs'
import { ManualAllocationModal } from '@/components/admin/ManualAllocationModal'
import {
  Users,
  Plus,
  UserCheck,
  UserX,
  BarChart3,
  Shuffle,
  Loader2,
  UserPlus
} from 'lucide-react'

interface AccommodationStats {
  totalRegistrations: number
  allocatedRegistrations: number
  unallocatedRegistrations: number
  allocationRate: number
  totalRooms: number
  activeRooms: number
  totalCapacity: number
  occupiedSpaces: number
  availableSpaces: number
  occupancyRate: number
}

interface Room {
  id: string
  name: string
  gender: string
  capacity: number
  isActive: boolean
  description?: string
  occupancy: number
  availableSpaces: number
  occupancyRate: number
  allocations: Array<{
    id: string
    registration: {
      id: string
      fullName: string
      gender: string
      dateOfBirth: string
      phoneNumber: string
      emailAddress: string
    }
  }>
}

export default function AccommodationsPage() {
  const [stats, setStats] = useState<AccommodationStats | null>(null)
  const [roomsByGender, setRoomsByGender] = useState<Record<string, Room[]>>({})
  const [unallocatedByGender, setUnallocatedByGender] = useState<Record<string, any[]>>({})
  const [loading, setLoading] = useState(true)
  const [showRoomModal, setShowRoomModal] = useState(false)
  const [showAllocationModal, setShowAllocationModal] = useState(false)
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [showPersonPreview, setShowPersonPreview] = useState(false)
  const [selectedPersonId, setSelectedPersonId] = useState<string | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [showManualAllocationModal, setShowManualAllocationModal] = useState(false)
  const [activeGenderTab, setActiveGenderTab] = useState<'Male' | 'Female'>('Male')

  // Pagination state - 4 rooms before pagination
  const getItemsPerPage = () => {
    return 4 // Fixed 4 rooms per page
  }

  const [malePagination, setMalePagination] = useState({ currentPage: 1, itemsPerPage: getItemsPerPage() })
  const [femalePagination, setFemalePagination] = useState({ currentPage: 1, itemsPerPage: getItemsPerPage() })

  const { currentUser, isRole } = useUser()
  const { success: showSuccess, error: showError } = useToast()

  // Permission helpers
  const canExport = () => {
    const userRole = currentUser?.role?.name || ''
    return ['Super Admin', 'Admin', 'Manager'].includes(userRole)
  }

  const canAutoAllocate = () => {
    const userRole = currentUser?.role?.name || ''
    return ['Super Admin', 'Admin', 'Manager'].includes(userRole)
  }

  const canEditRooms = () => {
    const userRole = currentUser?.role?.name || ''
    return ['Super Admin', 'Admin', 'Manager'].includes(userRole)
  }

  const canCreateRooms = () => {
    const userRole = currentUser?.role?.name || ''
    return ['Super Admin', 'Admin', 'Manager'].includes(userRole)
  }

  const canViewPersonDetails = () => {
    const userRole = currentUser?.role?.name || ''
    return ['Super Admin', 'Admin', 'Manager', 'Staff'].includes(userRole)
  }

  const canRemoveAllocations = () => {
    const userRole = currentUser?.role?.name || ''
    return ['Super Admin', 'Admin', 'Manager', 'Staff'].includes(userRole)
  }

  const isViewerOnly = () => {
    return isRole('Viewer')
  }

  const showToast = (title: string, type: 'success' | 'error' | 'warning' | 'info') => {
    if (type === 'success') {
      showSuccess(title)
    } else if (type === 'error') {
      showError(title)
    }
  }

  const fetchAccommodationData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/accommodations')

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch accommodation data')
      }

      const data = await response.json()
      setStats(data.stats)
      setRoomsByGender(data.roomsByGender || {})
      setUnallocatedByGender(data.unallocatedByGender || {})
    } catch (error) {
      console.error('Error fetching accommodation data:', error)
      const errorMessage = parseApiError(error)
      setError(errorMessage.description)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAccommodationData()
  }, [])

  const handleCreateRoom = () => {
    setSelectedRoom(null)
    setShowRoomModal(true)
  }

  const handleEditRoom = (room: Room) => {
    setSelectedRoom(room)
    setShowRoomModal(true)
  }

  const handleRoomSaved = () => {
    setShowRoomModal(false)
    setSelectedRoom(null)
    fetchAccommodationData()
    showToast('Room saved successfully', 'success')
  }

  const handleAutoAllocate = () => {
    setShowAllocationModal(true)
  }

  const handleAllocationComplete = (result: any) => {
    setShowAllocationModal(false)
    fetchAccommodationData()
    setRefreshTrigger(prev => prev + 1)
    showToast(`Successfully allocated ${result.totalAllocated} registrations`, 'success')
  }

  const handlePersonPreview = (registrationId: string) => {
    setSelectedPersonId(registrationId)
    setShowPersonPreview(true)
  }

  const handlePersonPreviewClose = () => {
    setShowPersonPreview(false)
    setSelectedPersonId(null)
  }

  const handleRemoveAllocationFromPreview = () => {
    fetchAccommodationData()
    setRefreshTrigger(prev => prev + 1)
  }

  const handleManualAllocate = () => {
    setShowManualAllocationModal(true)
  }

  const handleManualAllocationSuccess = () => {
    setShowManualAllocationModal(false)
    fetchAccommodationData()
    setRefreshTrigger(prev => prev + 1)
    showToast('Manual allocation successful', 'success')
  }

  // Pagination helper functions
  const getPaginatedRooms = (rooms: Room[], pagination: { currentPage: number; itemsPerPage: number }) => {
    const startIndex = (pagination.currentPage - 1) * pagination.itemsPerPage
    const endIndex = startIndex + pagination.itemsPerPage
    return rooms.slice(startIndex, endIndex)
  }

  const getTotalPages = (totalItems: number, itemsPerPage: number) => {
    return Math.ceil(totalItems / itemsPerPage)
  }

  const handlePageChange = (gender: 'Male' | 'Female', newPage: number) => {
    if (gender === 'Male') {
      setMalePagination(prev => ({ ...prev, currentPage: newPage }))
    } else {
      setFemalePagination(prev => ({ ...prev, currentPage: newPage }))
    }
  }

  if (loading) {
    return (
      <ProtectedRoute requiredRoles={['Super Admin', 'Admin', 'Manager', 'Staff', 'Viewer']}>
        <AdminLayoutNew title="Accommodations" description="Manage room allocations and housing arrangements">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
          </div>
        </AdminLayoutNew>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requiredRoles={['Super Admin', 'Admin', 'Manager', 'Staff', 'Viewer']}>
      <AdminLayoutNew title="Accommodations" description="Manage room allocations and housing arrangements">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
          <Card className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Total Registrations</p>
                <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">{stats?.totalRegistrations || 0}</p>
              </div>
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                <Users className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Allocated</p>
                <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">{stats?.allocatedRegistrations || 0}</p>
                <p className="font-apercu-regular text-xs text-green-600 truncate">{stats?.allocationRate || 0}% allocated</p>
              </div>
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                <UserCheck className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Unallocated</p>
                <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900 ">{stats?.unallocatedRegistrations || 0}</p>
                <p className="font-apercu-regular text-xs text-amber-600 truncate">Pending allocation</p>
              </div>
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                <UserX className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
          </Card>

          <Card className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="font-apercu-medium text-sm text-gray-600 mb-1 truncate">Room Occupancy</p>
                <p className="font-apercu-bold text-xl sm:text-2xl text-gray-900">{stats?.occupancyRate || 0}%</p>
                <p className="font-apercu-regular text-xs text-gray-600 truncate">{stats?.occupiedSpaces || 0}/{stats?.totalCapacity || 0} spaces</p>
              </div>
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center flex-shrink-0 ml-3">
                <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
            </div>
          </Card>
        </div>

        {/* Action Buttons */}
        {!isViewerOnly() && (
          <div className="flex flex-col sm:flex-row gap-4 mb-8">
            {canCreateRooms() && (
              <Button
                onClick={handleCreateRoom}
                className="font-apercu-medium bg-indigo-600 hover:bg-indigo-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add New Room
              </Button>
            )}

            {canAutoAllocate() && (
              <Button
                onClick={handleAutoAllocate}
                variant="outline"
                className="font-apercu-medium"
                disabled={!stats?.unallocatedRegistrations}
              >
                <Shuffle className="h-4 w-4 mr-2" />
                Auto Allocate Rooms
              </Button>
            )}

            {canAutoAllocate() && (
              <Button
                onClick={handleManualAllocate}
                variant="outline"
                className="font-apercu-medium border-green-200 text-green-700 hover:bg-green-50"
                disabled={!stats?.unallocatedRegistrations}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Manual Allocation
              </Button>
            )}
          </div>
        )}

        {/* Search and Export */}
        <AccommodationSearchExport
          onPersonSelect={handlePersonPreview}
          refreshTrigger={refreshTrigger}
          canExport={canExport()}
          canViewPersonDetails={canViewPersonDetails()}
          isViewerOnly={isViewerOnly()}
        />

        {/* Unallocated Registrations */}
        {Object.keys(unallocatedByGender).length > 0 && (
          <div className="my-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              <h2 className="font-apercu-bold text-xl text-gray-900 mb-2 sm:mb-0">Unallocated Registrations</h2>
              <Badge variant="warning" className="font-apercu-medium w-fit">
                {Object.values(unallocatedByGender).reduce((total, regs) => total + regs.length, 0)} total unallocated
              </Badge>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {Object.entries(unallocatedByGender).map(([gender, registrations]) => (
                <Card key={gender} className={`p-4 sm:p-6 border-2 ${
                  gender === 'Male'
                    ? 'bg-gradient-to-br from-blue-50 to-cyan-50 border-blue-100'
                    : 'bg-gradient-to-br from-pink-50 to-rose-50 border-pink-100'
                }`}>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                    <div className="flex items-center space-x-3 mb-2 sm:mb-0">
                      <div className={`h-8 w-8 rounded-lg flex items-center justify-center shadow-sm ${
                        gender === 'Male'
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600'
                          : 'bg-gradient-to-r from-pink-500 to-rose-500'
                      }`}>
                        <Users className="h-4 w-4 text-white" />
                      </div>
                      <h3 className={`font-apercu-bold text-lg ${
                        gender === 'Male' ? 'text-blue-900' : 'text-pink-900'
                      }`}>{gender}</h3>
                    </div>
                    <Badge className={`font-apercu-medium w-fit ${
                      gender === 'Male'
                        ? 'bg-amber-100 text-amber-800 border-amber-200'
                        : 'bg-amber-100 text-amber-800 border-amber-200'
                    }`}>
                      {registrations.length} unallocated
                    </Badge>
                  </div>
                  <div className="space-y-3">
                    {registrations.slice(0, 8).map((reg) => (
                      <div key={reg.id} className={`flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 rounded-lg border ${
                        gender === 'Male'
                          ? 'bg-white border-blue-100 hover:bg-blue-50'
                          : 'bg-white border-pink-100 hover:bg-pink-50'
                      } transition-colors`}>
                        <div className="flex-1 mb-2 sm:mb-0">
                          <span className="font-apercu-medium text-sm text-gray-900 block">{reg.fullName}</span>
                          <span className="font-apercu-regular text-xs text-gray-500">
                            {new Date().getFullYear() - new Date(reg.dateOfBirth).getFullYear()} years old
                          </span>
                        </div>
                        {canViewPersonDetails() && (
                          <button
                            onClick={() => handlePersonPreview(reg.id)}
                            className={`font-apercu-medium text-xs transition-colors w-fit ${
                              gender === 'Male'
                                ? 'text-blue-600 hover:text-blue-700'
                                : 'text-pink-600 hover:text-pink-700'
                            }`}
                          >
                            View Details
                          </button>
                        )}
                      </div>
                    ))}
                    {registrations.length > 8 && (
                      <div className="text-center pt-2">
                        <p className="font-apercu-regular text-sm text-gray-500">
                          And {registrations.length - 8} more...
                        </p>
                      </div>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Rooms by Gender - Beautiful Tabs */}
        <div className="mt-8 mb-8">
          <div className="mb-6">
            <GenderTabs
              activeTab={activeGenderTab}
              onTabChange={setActiveGenderTab}
              maleCount={roomsByGender.Male?.length || 0}
              femaleCount={roomsByGender.Female?.length || 0}
              className="max-w-2xl mx-auto"
            />
          </div>

          {/* Male Rooms Tab Content */}
          {activeGenderTab === 'Male' && roomsByGender.Male && (
            <GenderTabContent gender="Male" className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {getPaginatedRooms(roomsByGender.Male, malePagination).map((room) => (
                  <RoomCard
                    key={room.id}
                    room={room}
                    onEdit={handleEditRoom}
                    onRefresh={fetchAccommodationData}
                    onPersonPreview={handlePersonPreview}
                    canEditRooms={canEditRooms()}
                    canViewPersonDetails={canViewPersonDetails()}
                    canRemoveAllocations={canRemoveAllocations()}
                  />
                ))}
              </div>

              {roomsByGender.Male.length > malePagination.itemsPerPage && (
                <PaginationControls
                  currentPage={malePagination.currentPage}
                  totalPages={getTotalPages(roomsByGender.Male.length, malePagination.itemsPerPage)}
                  onPageChange={(page) => handlePageChange('Male', page)}
                  totalItems={roomsByGender.Male.length}
                  itemsPerPage={malePagination.itemsPerPage}
                  className="pt-4 border-t border-blue-200"
                  theme="blue"
                />
              )}
            </GenderTabContent>
          )}

          {/* Female Rooms Tab Content */}
          {activeGenderTab === 'Female' && roomsByGender.Female && (
            <GenderTabContent gender="Female" className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {getPaginatedRooms(roomsByGender.Female, femalePagination).map((room) => (
                  <RoomCard
                    key={room.id}
                    room={room}
                    onEdit={handleEditRoom}
                    onRefresh={fetchAccommodationData}
                    onPersonPreview={handlePersonPreview}
                    canEditRooms={canEditRooms()}
                    canViewPersonDetails={canViewPersonDetails()}
                    canRemoveAllocations={canRemoveAllocations()}
                  />
                ))}
              </div>

              {roomsByGender.Female.length > femalePagination.itemsPerPage && (
                <PaginationControls
                  currentPage={femalePagination.currentPage}
                  totalPages={getTotalPages(roomsByGender.Female.length, femalePagination.itemsPerPage)}
                  onPageChange={(page) => handlePageChange('Female', page)}
                  totalItems={roomsByGender.Female.length}
                  itemsPerPage={femalePagination.itemsPerPage}
                  className="pt-4 border-t border-pink-200"
                  theme="pink"
                />
              )}
            </GenderTabContent>
          )}
        </div>

        {/* Modals */}
        <RoomSetupModal
          isOpen={showRoomModal}
          onClose={() => setShowRoomModal(false)}
          onSave={handleRoomSaved}
          room={selectedRoom}
        />

        <AllocationSetupModal
          isOpen={showAllocationModal}
          onClose={() => setShowAllocationModal(false)}
          onComplete={handleAllocationComplete}
          unallocatedCount={stats?.unallocatedRegistrations || 0}
        />

        <ManualAllocationModal
          isOpen={showManualAllocationModal}
          onClose={() => setShowManualAllocationModal(false)}
          onSuccess={handleManualAllocationSuccess}
        />

        {/* Person Preview Modal */}
        <PersonPreviewModal
          isOpen={showPersonPreview}
          onClose={handlePersonPreviewClose}
          registrationId={selectedPersonId}
          onRemoveAllocation={handleRemoveAllocationFromPreview}
          canRemoveAllocations={canRemoveAllocations()}
        />

        {/* Error Modal */}
        <ErrorModal
          isOpen={!!error}
          onClose={() => setError(null)}
          type="error"
          title="Error"
          description={error || ''}
        />
      </AdminLayoutNew>
    </ProtectedRoute>
  )
}
