'use client'

import { ReactNode } from 'react'
import { NotificationProvider } from '@/contexts/NotificationContext'
import { MessageProvider } from '@/contexts/MessageContext'
import { UserProvider } from '@/contexts/UserContext'
import { ToastProvider } from '@/contexts/ToastContext'

interface AdminLayoutProps {
  children: ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <UserProvider>
      <NotificationProvider>
        <MessageProvider>
          <ToastProvider>
            {children}
          </ToastProvider>
        </MessageProvider>
      </NotificationProvider>
    </UserProvider>
  )
}
